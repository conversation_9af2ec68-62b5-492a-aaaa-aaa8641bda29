import { useState } from 'react'

export default function SearchBar({ onSearch, onLocationSearch, initial = '' }) {
  const [value, setValue] = useState(initial)
  
  function handleSubmit(e) {
    e.preventDefault()
    if (value.trim()) {
      onSearch(value.trim())
    }
  }

  return (
    <form onSubmit={handleSubmit} className="flex w-full gap-2 md:w-auto">
      <input
        value={value}
        onChange={e => setValue(e.target.value)}
        placeholder="Search city (e.g., London)"
        className="flex-1 md:w-64 rounded-xl bg-slate-800/70 px-4 py-3 text-white placeholder-slate-400 outline-none ring-1 ring-slate-700 focus:ring-sky-400 backdrop-blur-sm"
      />
      <button 
        type="button"
        onClick={onLocationSearch}
        className="rounded-xl bg-slate-600/80 px-4 py-3 text-white hover:bg-slate-500/80 backdrop-blur-sm transition-colors"
        title="Use current location"
      >
        📍
      </button>
      <button 
        type="submit"
        className="rounded-xl bg-sky-500 px-5 py-3 font-medium text-slate-900 hover:bg-sky-400 transition-colors"
      >
        Search
      </button>
    </form>
  )
}


