const useProxy = import.meta.env.VITE_USE_PROXY === 'true'
const proxyBase = import.meta.env.VITE_PROXY_BASE || ''
const API_KEY = import.meta.env.VITE_OWM_API_KEY
const BASE = 'https://api.openweathermap.org/data/2.5'

function qs(params) {
  return new URLSearchParams(params).toString()
}

export async function fetchCurrent(city) {
  if (import.meta.env.DEV) console.log('[api] mode:', useProxy ? 'proxy' : 'direct', useProxy ? proxyBase : 'openweathermap.org')
  if (useProxy) {
    const res = await fetch(`${proxyBase}/api/weather?city=${encodeURIComponent(city)}`)
    if (!res.ok) throw new Error('Failed to fetch current weather')
    return res.json()
  } else {
    const res = await fetch(`${BASE}/weather?${qs({ q: city, appid: API_KEY, units: 'metric' })}`)
    if (!res.ok) throw new Error('City not found')
    return res.json()
  }
}

export async function fetchForecast(city) {
  if (useProxy) {
    const res = await fetch(`${proxyBase}/api/forecast?city=${encodeURIComponent(city)}`)
    if (!res.ok) throw new Error('Failed to fetch forecast')
    return res.json()
  } else {
    const res = await fetch(`${BASE}/forecast?${qs({ q: city, appid: API_KEY, units: 'metric' })}`)
    if (!res.ok) throw new Error('City not found')
    return res.json()
  }
}


