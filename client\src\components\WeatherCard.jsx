export default function WeatherCard({ data, city, onToggleFavorite, isFavorite }) {
  const { main, weather, wind } = data
  const icon = weather[0]?.icon
  const description = weather[0]?.description

  return (
    <div className="card p-6 bg-white/10 backdrop-blur-sm border border-white/20">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h2 className="text-2xl font-bold text-white mb-1">{city}</h2>
          <p className="text-slate-200 capitalize">{description}</p>
        </div>
        <div className="flex items-center gap-3">
          {icon && (
            <img 
              src={`https://openweathermap.org/img/wn/${icon}@2x.png`}
              alt={description}
              className="w-16 h-16"
            />
          )}
          <button
            onClick={onToggleFavorite}
            className={`text-2xl transition-transform hover:scale-110 ${
              isFavorite ? 'text-yellow-400' : 'text-slate-400 hover:text-yellow-400'
            }`}
            title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
          >
            {isFavorite ? '⭐' : '☆'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-3xl font-bold text-white">{Math.round(main.temp)}°</div>
          <div className="text-sm text-slate-300">Temperature</div>
        </div>
        <div className="text-center">
          <div className="text-xl font-semibold text-white">{main.humidity}%</div>
          <div className="text-sm text-slate-300">Humidity</div>
        </div>
        <div className="text-center">
          <div className="text-xl font-semibold text-white">{Math.round(wind.speed)} m/s</div>
          <div className="text-sm text-slate-300">Wind Speed</div>
        </div>
        <div className="text-center">
          <div className="text-xl font-semibold text-white">{Math.round(main.feels_like)}°</div>
          <div className="text-sm text-slate-300">Feels Like</div>
        </div>
      </div>
    </div>
  )
}


