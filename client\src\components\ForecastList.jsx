export default function ForecastList({ days }) {
  return (
    <div className="card p-6 bg-white/10 backdrop-blur-sm border border-white/20">
      <h3 className="text-xl font-bold text-white mb-4">5-Day Forecast</h3>
      <div className="grid gap-4 md:grid-cols-5">
        {days.map((day, i) => (
          <div key={i} className="text-center p-4 rounded-lg bg-white/5 border border-white/10">
            <div className="font-medium text-white mb-2">{day.date}</div>
            {day.icon && (
              <img 
                src={`https://openweathermap.org/img/wn/${day.icon}.png`}
                alt={day.description}
                className="w-12 h-12 mx-auto mb-2"
              />
            )}
            <div className="text-lg font-bold text-white">{Math.round(day.temp)}°</div>
            <div className="text-sm text-slate-300 capitalize">{day.description}</div>
            <div className="text-xs text-slate-400 mt-1">{day.humidity}% humidity</div>
          </div>
        ))}
      </div>
    </div>
  )
}


