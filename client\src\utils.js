export function kelvinToC(k) { return k - 273.15 }
export function toC(n) { return Math.round(n) + '°C' }
export function toSpeed(ms) { return Math.round(ms) + ' m/s' }

// Group 3-hourly forecast list into daily buckets and pick midday entries
export function summarizeForecast(list) {
  const byDate = list.reduce((acc, item) => {
    const d = item.dt_txt.split(' ')[0]
    acc[d] = acc[d] || []
    acc[d].push(item)
    return acc
  }, {})

  return Object.entries(byDate).slice(0, 5).map(([date, entries]) => {
    // pick item closest to 12:00:00
    const target = entries.reduce((best, item) => {
      const hour = new Date(item.dt_txt).getHours()
      const diff = Math.abs(hour - 12)
      return !best || diff < best.diff ? { item, diff } : best
    }, null).item

    const temps = entries.map(e => e.main.temp)
    const min = Math.min(...temps)
    const max = Math.max(...temps)

    return { date, min, max, icon: target.weather[0].icon, desc: target.weather[0].description, humidity: target.main.humidity, wind: target.wind.speed }
  })
}

export function iconUrl(code) { return `https://openweathermap.org/img/wn/${code}@2x.png` }


