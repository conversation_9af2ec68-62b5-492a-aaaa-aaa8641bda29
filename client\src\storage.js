const FAVORITES_KEY = 'wd:favorites'
const RECENT_KEY = 'wd:recent'

export function loadFavorites() {
  try {
    return JSON.parse(localStorage.getItem(FAVORITES_KEY)) || []
  } catch {
    return []
  }
}

export function saveFavorites(favorites) {
  localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites))
}

export function loadRecent() {
  try {
    return JSON.parse(localStorage.getItem(RECENT_KEY)) || []
  } catch {
    return []
  }
}

export function saveRecent(city, currentList) {
  const updated = [city, ...currentList.filter(c => c !== city)].slice(0, 5)
  localStorage.setItem(RECENT_KEY, JSON.stringify(updated))
  return updated
}


